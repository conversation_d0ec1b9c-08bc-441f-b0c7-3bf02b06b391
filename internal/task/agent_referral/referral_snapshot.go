package agent_referral

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type SnapshotTask struct {
	repo repo.InvitationRepo
}

func NewSnapshotTask(repo repo.InvitationRepo) *SnapshotTask {
	return &SnapshotTask{
		repo: repo,
	}
}

// UpdateAllReferralSnapshots updates agent referral snapshot statistics for all users
// This includes direct count, total downline count, total volume USD, total rewards distributed, and upline relationships
func (s *SnapshotTask) UpdateAllReferralSnapshots() {
	// global.GVA_LOG.Info("Starting agent referral snapshot update for all users")

	var userIDs []uuid.UUID
	global.GVA_DB.Model(&model.User{}).Pluck("id", &userIDs)

	// global.GVA_LOG.Info("Found users to process", zap.Int("user_count", len(userIDs)))

	processedCount := 0
	errorCount := 0

	for _, userID := range userIDs {
		if err := s.updateUserReferralSnapshot(userID); err != nil {
			// global.GVA_LOG.Error("Failed to update referral snapshot for user",
			// 	zap.String("user_id", userID.String()),
			// 	zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	// global.GVA_LOG.Info("Referral snapshot update completed",
	// 	zap.Int("total_users", len(userIDs)),
	// 	zap.Int("processed_count", processedCount),
	// 	zap.Int("error_count", errorCount))
}

// updateUserReferralSnapshot updates referral snapshot for a single user
func (s *SnapshotTask) updateUserReferralSnapshot(userID uuid.UUID) error {
	// global.GVA_LOG.Debug("Processing referral snapshot for user", zap.String("user_id", userID.String()))

	// Calculate direct referral count (depth = 1)
	var directCount int64
	global.GVA_DB.Model(&model.Referral{}).
		Where("referrer_id = ? AND depth = 1", userID).
		Count(&directCount)

	// Calculate total downline count (depth <= 3)
	var totalDownlineCount int64
	global.GVA_DB.Model(&model.Referral{}).
		Where("referrer_id = ? AND depth <= 3", userID).
		Count(&totalDownlineCount)

	// TradingUserCount: How many addresses of the invitees invited by the current user have conducted MEME or contract transactions.
	// As long as one of the transactions is completed, it is considered as the number of transactions.
	var tradingUserCount int64
	err := global.GVA_DB.Model(&model.Referral{}).
		Joins("LEFT JOIN hyper_liquid_transactions hlt ON referrals.user_id = hlt.user_id AND hlt.status = 'filled'").
		Joins("LEFT JOIN affiliate_transactions at ON referrals.user_id = at.user_id AND at.status = 'Completed'").
		Where("referrals.referrer_id = ? AND (hlt.user_id IS NOT NULL OR at.user_id IS NOT NULL)", userID).
		Distinct("referrals.user_id").
		Count(&tradingUserCount).Error
	if err != nil {
		return err
	}

	// TotalPerpsVolumeUSD = avg_price * totalSz, status = filled
	var totalPerpsVolumeUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
		Joins("JOIN referrals ON hyper_liquid_transactions.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND hyper_liquid_transactions.status = ?", userID, "filled").
		Select("COALESCE(SUM(hyper_liquid_transactions.avg_price * hyper_liquid_transactions.total_sz), 0)").
		Scan(&totalPerpsVolumeUSD).Error
	if err != nil {
		return err
	}

	// TotalPerps FeesQuery the transaction volume of HyperLiquidTransaction with BuildFee as the handling fee and status as filled
	var totalPerpsFees decimal.Decimal
	err = global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
		Joins("JOIN referrals ON hyper_liquid_transactions.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND hyper_liquid_transactions.status = ?", userID, "filled").
		Select("COALESCE(SUM(hyper_liquid_transactions.build_fee), 0)").
		Scan(&totalPerpsFees).Error
	if err != nil {
		return err
	}

	// TotalPerpsFeesPaid Query CommissionAmount in CommissionLedger, the commission amount with status CLAIMED, and calculate the total commission
	var totalPerpsFeesPaid decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Joins("JOIN referrals ON commission_ledger.source_user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND commission_ledger.status = ?", userID, "CLAIMED").
		Select("COALESCE(SUM(commission_ledger.commission_amount), 0)").
		Scan(&totalPerpsFeesPaid).Error
	if err != nil {
		return err
	}

	// TotalPerpsFeesUnPaid  The commission amount of the PENDING_CLAIM status, calculate the total commission
	var totalPerpsFeesUnPaid decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Joins("JOIN referrals ON commission_ledger.source_user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND commission_ledger.status = ?", userID, "PENDING_CLAIM").
		Select("COALESCE(SUM(commission_ledger.commission_amount), 0)").
		Scan(&totalPerpsFeesUnPaid).Error
	if err != nil {
		return err
	}

	// TotalPerpsTradesCount: How many addresses of the invitees invited by the current user have conducted contract transactions.
	// As long as one of the transactions is completed, it is considered as the number of transactions.
	var totalPerpsTradesCount int64
	err = global.GVA_DB.Model(&model.HyperLiquidTransaction{}).
		Joins("JOIN referrals ON hyper_liquid_transactions.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND hyper_liquid_transactions.status = ?", userID, "filled").
		Distinct("hyper_liquid_transactions.user_id").
		Count(&totalPerpsTradesCount).Error
	if err != nil {
		return err
	}

	// TotalMemeVolumeUSD  SolPriceUSD * CashbackAmountSOL
	var totalMemeVolumeUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Joins("JOIN referrals ON activity_cashback.user_id = referrals.user_id").
		Where("referrals.referrer_id = ?", userID).
		Select("COALESCE(SUM(activity_cashback.sol_price_usd * activity_cashback.cashback_amount_sol), 0)").
		Scan(&totalMemeVolumeUSD).Error
	if err != nil {
		return err
	}

	// TotalMemeFees Query the CashbackAmountUSD of ActivityCashback
	var totalMemeFees decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Joins("JOIN referrals ON activity_cashback.user_id = referrals.user_id").
		Where("referrals.referrer_id = ?", userID).
		Select("COALESCE(SUM(activity_cashback.cashback_amount_usd), 0)").
		Scan(&totalMemeFees).Error
	if err != nil {
		return err
	}

	// TotalMemeFeesPaid Query the CashbackAmountUSD of ActivityCashback, the commission amount of the CLAIMED status, and calculate the total commission
	var totalMemeFeesPaid decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Joins("JOIN referrals ON activity_cashback.user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND activity_cashback.status = ?", userID, "CLAIMED").
		Select("COALESCE(SUM(activity_cashback.cashback_amount_usd), 0)").
		Scan(&totalMemeFeesPaid).Error
	if err != nil {
		return err
	}

	// TotalMemeFeesUnPaid The commission amount of the PENDING_CLAIM status, calculate the total commission
	var totalMemeFeesUnPaid decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Joins("JOIN referrals ON commission_ledger.source_user_id = referrals.user_id").
		Where("referrals.referrer_id = ? AND commission_ledger.status = ?", userID, "PENDING_CLAIM").
		Select("COALESCE(SUM(commission_ledger.commission_amount), 0)").
		Scan(&totalMemeFeesUnPaid).Error
	if err != nil {
		return err
	}

	// TotalMemeTradesCount: How many addresses of the invitees invited by the current user have conducted Meme transactions.
	// As long as one of the transactions is completed, it is considered as the number of transactions.
	var totalMemeTradesCount int64
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Joins("JOIN referrals ON activity_cashback.user_id = referrals.user_id").
		Where("referrals.referrer_id = ?", userID).
		Distinct("activity_cashback.user_id").
		Count(&totalMemeTradesCount).Error
	if err != nil {
		return err
	}

	// TotalCommissionEarnedUSD The user queries the CommissionAmount field in the CommissionLedger table to find the total amount of commission earned.
	var totalCommissionEarnedUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ?", userID).
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&totalCommissionEarnedUSD).Error
	if err != nil {
		return err
	}

	// ClaimedCommissionUSD Query the CommissionAmount field of the CommissionLedger table, the commission amount of the CLAIMED status, and calculate the total commission
	var claimedCommissionUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "CLAIMED").
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&claimedCommissionUSD).Error
	if err != nil {
		return err
	}

	// UnclaimedCommissionUSD Query the CommissionAmount field of the CommissionLedger table,
	// the commission amount with the status of PENDING_CLAIM, and calculate the total commission
	var unclaimedCommissionUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.CommissionLedger{}).
		Where("recipient_user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Select("COALESCE(SUM(commission_amount), 0)").
		Scan(&unclaimedCommissionUSD).Error
	if err != nil {
		return err
	}

	// TotalCashbackEarnedUSD Query the CashbackAmountUSD field of ActivityCashback to calculate the total rebate
	var totalCashbackEarnedUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Where("user_id = ?", userID).
		Select("COALESCE(SUM(cashback_amount_usd), 0)").
		Scan(&totalCashbackEarnedUSD).Error
	if err != nil {
		return err
	}

	// ClaimedCashbackUSD Query the CashbackAmountUSD field of ActivityCashback,
	// the commission amount of the CLAIMED status, and calculate the total commission
	var claimedCashbackUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Where("user_id = ? AND status = ?", userID, "CLAIMED").
		Select("COALESCE(SUM(cashback_amount_usd), 0)").
		Scan(&claimedCashbackUSD).Error
	if err != nil {
		return err
	}

	// UnclaimedCashbackUSD Query the CashbackAmountUSD field of ActivityCashback,
	// the commission amount of the status PENDING_CLAIM, and calculate the total commission
	var unclaimedCashbackUSD decimal.Decimal
	err = global.GVA_DB.Model(&model.ActivityCashback{}).
		Where("user_id = ? AND status = ?", userID, "PENDING_CLAIM").
		Select("COALESCE(SUM(cashback_amount_usd), 0)").
		Scan(&unclaimedCashbackUSD).Error
	if err != nil {
		return err
	}

	// Get upline relationships (L1, L2, L3)
	var l1UplineID, l2UplineID, l3UplineID *uuid.UUID
	var referral model.Referral
	err = global.GVA_DB.Where("user_id = ?", userID).First(&referral).Error
	if err == nil && referral.ReferrerID != nil {
		// L1 upline is the direct referrer
		l1UplineID = referral.ReferrerID

		// Find L2 upline (referrer of L1)
		var l1Referral model.Referral
		err = global.GVA_DB.Where("user_id = ?", *l1UplineID).First(&l1Referral).Error
		if err == nil && l1Referral.ReferrerID != nil {
			l2UplineID = l1Referral.ReferrerID

			// Find L3 upline (referrer of L2)
			var l2Referral model.Referral
			err = global.GVA_DB.Where("user_id = ?", *l2UplineID).First(&l2Referral).Error
			if err == nil && l2Referral.ReferrerID != nil {
				l3UplineID = l2Referral.ReferrerID
			}
		}
	}

	// Create or update ReferralSnapshot
	var snapshot model.ReferralSnapshot
	err = global.GVA_DB.Where("user_id = ?", userID).First(&snapshot).Error
	if err == gorm.ErrRecordNotFound {
		// Create new snapshot
		snapshot = model.ReferralSnapshot{
			UserID:                   userID,
			DirectCount:              int(directCount),
			TotalDownlineCount:       int(totalDownlineCount),
			TradingUserCount:         int(tradingUserCount),
			TotalPerpsVolumeUSD:      totalPerpsVolumeUSD,
			TotalPerpsFees:           totalPerpsFees,
			TotalPerpsFeesPaid:       totalPerpsFeesPaid,
			TotalPerpsFeesUnPaid:     totalPerpsFeesUnPaid,
			TotalPerpsTradesCount:    totalPerpsTradesCount,
			TotalMemeVolumeUSD:       totalMemeVolumeUSD,
			TotalMemeFees:            totalMemeFees,
			TotalMemeFeesPaid:        totalMemeFeesPaid,
			TotalMemeFeesUnPaid:      totalMemeFeesUnPaid,
			TotalMemeTradesCount:     totalMemeTradesCount,
			TotalCommissionEarnedUSD: totalCommissionEarnedUSD,
			ClaimedCommissionUSD:     claimedCommissionUSD,
			UnclaimedCommissionUSD:   unclaimedCommissionUSD,
			TotalCashbackEarnedUSD:   totalCashbackEarnedUSD,
			ClaimedCashbackUSD:       claimedCashbackUSD,
			UnclaimedCashbackUSD:     unclaimedCashbackUSD,
			L1UplineID:               l1UplineID,
			L2UplineID:               l2UplineID,
			L3UplineID:               l3UplineID,
		}
		err = global.GVA_DB.Create(&snapshot).Error
		if err != nil {
			return err
		}
		return nil
	} else if err == nil {
		// Update existing snapshot
		updates := model.ReferralSnapshot{
			DirectCount:              int(directCount),
			TotalDownlineCount:       int(totalDownlineCount),
			TradingUserCount:         int(tradingUserCount),
			TotalPerpsVolumeUSD:      totalPerpsVolumeUSD,
			TotalPerpsFees:           totalPerpsFees,
			TotalPerpsFeesPaid:       totalPerpsFeesPaid,
			TotalPerpsFeesUnPaid:     totalPerpsFeesUnPaid,
			TotalPerpsTradesCount:    totalPerpsTradesCount,
			TotalMemeVolumeUSD:       totalMemeVolumeUSD,
			TotalMemeFees:            totalMemeFees,
			TotalMemeFeesPaid:        totalMemeFeesPaid,
			TotalMemeFeesUnPaid:      totalMemeFeesUnPaid,
			TotalMemeTradesCount:     totalMemeTradesCount,
			TotalCommissionEarnedUSD: totalCommissionEarnedUSD,
			ClaimedCommissionUSD:     claimedCommissionUSD,
			UnclaimedCommissionUSD:   unclaimedCommissionUSD,
			TotalCashbackEarnedUSD:   totalCashbackEarnedUSD,
			ClaimedCashbackUSD:       claimedCashbackUSD,
			UnclaimedCashbackUSD:     unclaimedCashbackUSD,
			L1UplineID:               l1UplineID,
			L2UplineID:               l2UplineID,
			L3UplineID:               l3UplineID,
		}
		err = global.GVA_DB.Model(&snapshot).Updates(updates).Error
		if err != nil {
			return err
		}
	} else {
		return err
	}

	return nil
}

// CreateReferralTreeSnapshots Create referral tree snapshots
// Execute daily at midnight, create referral tree snapshots for each root user
func (s *SnapshotTask) CreateReferralTreeSnapshots() {
	global.GVA_LOG.Info("Starting referral tree snapshot task")

	// Get snapshot date (today)
	snapshotDate := time.Now().UTC()
	snapshotDateStr := snapshotDate.Format("2006-01-02")

	global.GVA_LOG.Info("Snapshot Date", zap.String("date", snapshotDateStr))

	// 1. select * from referrals where depth = 1; referral_id is the root user
	rootUsers, err := s.getRootUsers()
	if err != nil {
		global.GVA_LOG.Error("Failed to get root users", zap.Error(err))
		return
	}

	global.GVA_LOG.Info("Found root users", zap.Int("root_user_count", len(rootUsers)))

	processedCount := 0
	errorCount := 0

	for _, rootUser := range rootUsers {
		if err := s.processReferralTreeSnapshot(rootUser.ID, snapshotDate); err != nil {
			global.GVA_LOG.Error("Failed to process referral tree snapshot",
				zap.String("root_user_id", rootUser.ID.String()),
				zap.Error(err))
			errorCount++
		} else {
			processedCount++
		}
	}

	global.GVA_LOG.Info("Referral tree snapshot task completed",
		zap.String("date", snapshotDateStr),
		zap.Int("total_trees", len(rootUsers)),
		zap.Int("processed_count", processedCount),
		zap.Int("error_count", errorCount))
}

// getRootUsers Get all root users (users without referrers)
func (s *SnapshotTask) getRootUsers() ([]model.User, error) {
	var rootUsers []model.User

	// Query referral relationships with depth = 1, referrer_id is the root user
	err := global.GVA_DB.
		Joins("JOIN referrals ON users.id = referrals.referrer_id").
		Where("referrals.depth = 1").
		Distinct("users.id").
		Find(&rootUsers).Error

	if err != nil {
		return nil, fmt.Errorf("failed to query root users: %w", err)
	}

	return rootUsers, nil
}

// processReferralTreeSnapshot Process single referral tree snapshot
func (s *SnapshotTask) processReferralTreeSnapshot(rootUserID uuid.UUID, snapshotDate time.Time) error {
	// Check if snapshot for this date already exists
	existingSnapshot, err := s.getExistingTreeSnapshot(rootUserID, snapshotDate)
	if err != nil {
		return fmt.Errorf("failed to check existing snapshot: %w", err)
	}

	if existingSnapshot != nil {
		global.GVA_LOG.Debug("Snapshot already exists, skipping processing",
			zap.String("root_user_id", rootUserID.String()),
			zap.Time("snapshot_date", snapshotDate))
		return nil
	}

	// 2. depth=2 or other subordinate users
	// 3. Recursively query users - recursively query all subordinate users
	treeUsers, err := s.getAllUsersInTree(rootUserID)
	if err != nil {
		return fmt.Errorf("failed to get all users in tree: %w", err)
	}

	// 4. Calculate tree structure information
	treeInfo, err := s.calculateTreeInfo(rootUserID, treeUsers)
	if err != nil {
		return fmt.Errorf("failed to calculate tree info: %w", err)
	}

	// 5. Save tree snapshot and node snapshots
	err = s.saveTreeSnapshot(rootUserID, snapshotDate, treeInfo, treeUsers)
	if err != nil {
		return fmt.Errorf("failed to save tree snapshot: %w", err)
	}

	global.GVA_LOG.Debug("Referral tree snapshot processing completed",
		zap.String("root_user_id", rootUserID.String()),
		zap.Time("snapshot_date", snapshotDate),
		zap.Int("total_nodes", treeInfo.TotalNodes),
		zap.Int("max_depth", treeInfo.MaxDepth),
		zap.Int("direct_count", treeInfo.DirectCount))

	return nil
}

// getExistingTreeSnapshot Check if snapshot already exists
func (s *SnapshotTask) getExistingTreeSnapshot(rootUserID uuid.UUID, snapshotDate time.Time) (*model.ReferralTreeSnapshot, error) {
	var snapshot model.ReferralTreeSnapshot

	err := global.GVA_DB.Where("root_user_id = ? AND DATE(snapshot_date) = DATE(?)", rootUserID, snapshotDate).
		First(&snapshot).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return &snapshot, nil
}

// getAllUsersInTree Get all users in the tree
func (s *SnapshotTask) getAllUsersInTree(rootUserID uuid.UUID) ([]model.User, error) {
	var users []model.User

	// Recursively query all subordinate users
	query := `
		WITH RECURSIVE descendants AS (
			-- root user
			SELECT id, email, invitation_code, created_at, updated_at, deleted_at,
				   agent_level_id, level_grace_period_started_at, level_upgraded_at,
				   first_transaction_at, 0 as depth, NULL::uuid as parent_id, NULL::uuid as referrer_id
			FROM users WHERE id = ?
			UNION ALL
			-- Recursive query of subordinate users
			SELECT u.id, u.email, u.invitation_code, u.created_at, u.updated_at, u.deleted_at,
				   u.agent_level_id, u.level_grace_period_started_at, u.level_upgraded_at,
				   u.first_transaction_at, d.depth + 1, d.id, r.referrer_id
			FROM users u
			JOIN referrals r ON u.id = r.user_id
			JOIN descendants d ON d.id = r.referrer_id
		)
		SELECT * FROM descendants ORDER BY depth, id;
	`

	err := global.GVA_DB.Raw(query, rootUserID).Scan(&users).Error
	if err != nil {
		return nil, fmt.Errorf("failed to recursively query users: %w", err)
	}

	return users, nil
}

// TreeInfo Tree information structure
type TreeInfo struct {
	TotalNodes   int
	MaxDepth     int
	DirectCount  int
	ActiveUsers  int
	TradingUsers int
}

// calculateTreeInfo Calculate tree structure information
func (s *SnapshotTask) calculateTreeInfo(rootUserID uuid.UUID, users []model.User) (*TreeInfo, error) {
	if len(users) == 0 {
		return &TreeInfo{}, nil
	}

	info := &TreeInfo{
		TotalNodes: len(users),
	}

	// Calculate maximum depth and direct referral count
	maxDepth := 0
	directCount := 0
	activeUsers := 0
	tradingUsers := 0

	for _, user := range users {
		// Calculate user depth
		depth, err := s.calculateUserDepth(rootUserID, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user depth", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		if depth == 1 {
			directCount++
		}

		if depth > maxDepth {
			maxDepth = depth
		}

		// Count active users (users with transaction records)
		if user.FirstTransactionAt != nil {
			activeUsers++
			tradingUsers++
		}
	}

	info.MaxDepth = maxDepth
	info.DirectCount = directCount
	info.ActiveUsers = activeUsers
	info.TradingUsers = tradingUsers

	return info, nil
}

// calculateUserDepth Calculate user depth in the tree
func (s *SnapshotTask) calculateUserDepth(rootUserID, userID uuid.UUID) (int, error) {
	if rootUserID == userID {
		return 0, nil
	}

	var depth int
	query := `
		WITH RECURSIVE path AS (
			SELECT user_id, referrer_id, 1 as depth
			FROM referrals
			WHERE user_id = ?
			UNION ALL
			SELECT r.user_id, r.referrer_id, p.depth + 1
			FROM referrals r
			JOIN path p ON r.user_id = p.referrer_id
			WHERE p.referrer_id != ?
		)
		SELECT MAX(depth) FROM path;
	`

	err := global.GVA_DB.Raw(query, userID, rootUserID).Scan(&depth).Error
	if err != nil {
		return 0, err
	}

	return depth, nil
}

// saveTreeSnapshot Save tree snapshot and node snapshots
func (s *SnapshotTask) saveTreeSnapshot(rootUserID uuid.UUID, snapshotDate time.Time, treeInfo *TreeInfo, users []model.User) error {
	// Create tree snapshot
	treeSnapshot := &model.ReferralTreeSnapshot{
		RootUserID:          rootUserID,
		SnapshotDate:        snapshotDate,
		TotalNodes:          treeInfo.TotalNodes,
		MaxDepth:            treeInfo.MaxDepth,
		DirectCount:         treeInfo.DirectCount,
		ActiveUsers:         treeInfo.ActiveUsers,
		TradingUsers:        treeInfo.TradingUsers,
		InfiniteAgentUserID: nil,
		HasInfiniteAgent:    false,
		Description:         fmt.Sprintf("Agent Recommendation Tree Snapshot - Root User: %s, date: %s", rootUserID.String(), snapshotDate.Format("2006-01-02")),
		IsValid:             true,
	}

	err := global.GVA_DB.Create(treeSnapshot).Error
	if err != nil {
		return fmt.Errorf("failed to save tree snapshot: %w", err)
	}

	err = s.createTreeNodes(treeSnapshot.ID, users)
	if err != nil {
		return fmt.Errorf("Failed to create tree node snapshot: %w", err)
	}

	return nil
}

// createTreeNodes
func (s *SnapshotTask) createTreeNodes(treeSnapshotID uint, users []model.User) error {
	var treeNodes []model.ReferralTreeNode

	for i, user := range users {
		depth, err := s.calculateUserDepth(users[0].ID, user.ID)
		if err != nil {
			global.GVA_LOG.Warn("Failed to calculate user depth", zap.String("user_id", user.ID.String()), zap.Error(err))
			continue
		}

		// Get referrer ID
		var referrerID *uuid.UUID
		if depth > 0 {
			referrerID, err = s.getReferrerID(user.ID)
			if err != nil {
				global.GVA_LOG.Warn("Failed to get referrer ID", zap.String("user_id", user.ID.String()), zap.Error(err))
			}
		}

		isTrading := user.FirstTransactionAt != nil

		treeNode := model.ReferralTreeNode{
			TreeSnapshotID: treeSnapshotID,
			UserID:         user.ID,
			ParentUserID:   referrerID,
			ReferrerID:     referrerID,
			Depth:          depth,
			Level:          depth + 1,
			Position:       i + 1,
			IsActive:       true,
			IsTrading:      isTrading,
			AgentLevelID:   user.AgentLevelID,
		}

		treeNodes = append(treeNodes, treeNode)
	}

	if len(treeNodes) > 0 {
		err := global.GVA_DB.CreateInBatches(treeNodes, 100).Error
		if err != nil {
			return fmt.Errorf("failed to batch create tree nodes: %w", err)
		}
	}

	return nil
}

// getReferrerID Get user's referrer ID
func (s *SnapshotTask) getReferrerID(userID uuid.UUID) (*uuid.UUID, error) {
	var referral model.Referral

	err := global.GVA_DB.Where("user_id = ?", userID).First(&referral).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}

	return referral.ReferrerID, nil
}
