package activity_cashback

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
)

// TestConsecutiveCheckinFixesIntegration tests the fixes for consecutive check-in task completion issues
func TestConsecutiveCheckinFixesIntegration(t *testing.T) {

	t.Run("Test_1_Day_Milestone_Completion", func(t *testing.T) {
		// This test verifies that tasks with 1-day milestones can be completed successfully
		// Previously this would fail due to daily frequency check conflicts

		// Create test milestones with 1-day first milestone
		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50, Name: &model.MultilingualName{En: "Daily 1"}},
			{Days: 2, Points: 200, Name: &model.MultilingualName{En: "Daily 2"}},
			{Days: 4, Points: 200, Name: &model.MultilingualName{En: "Daily 4"}},
		}

		// Test that the milestone logic works correctly
		assert.Equal(t, 1, milestones[0].Days, "First milestone should be 1 day")
		assert.Equal(t, 50, milestones[0].Points, "First milestone should award 50 points")
		assert.NotNil(t, milestones[0].Name, "First milestone should have a name")
		assert.Equal(t, "Daily 1", milestones[0].Name.En, "First milestone name should be 'Daily 1'")

		// Test milestone progression logic
		service := &TaskProgressService{}

		// Test getNextMilestone for 1-day milestone scenario
		nextMilestone := service.getNextMilestone(milestones, 0) // No streak yet
		require.NotNil(t, nextMilestone, "Should have next milestone for 0 streak")
		assert.Equal(t, 1, nextMilestone.Days, "Next milestone should be 1 day")
		assert.Equal(t, 50, nextMilestone.Points, "Next milestone should be 50 points")

		// Test after 1 day streak
		nextMilestone = service.getNextMilestone(milestones, 1) // 1 day streak
		require.NotNil(t, nextMilestone, "Should have next milestone for 1 day streak")
		assert.Equal(t, 2, nextMilestone.Days, "Next milestone should be 2 days")
		assert.Equal(t, 200, nextMilestone.Points, "Next milestone should be 200 points")
	})

	t.Run("Test_Multiple_Completion_Prevention", func(t *testing.T) {
		// This test verifies that daily frequency constraints prevent multiple completions per day

		// Test the daily completion check logic
		today := time.Now().Truncate(24 * time.Hour)
		yesterday := today.Add(-24 * time.Hour)

		// Test case 1: Same day completion should be prevented
		lastCompletedToday := today
		assert.True(t, lastCompletedToday.Equal(today), "Same day completion should be detected")

		// Test case 2: Yesterday completion should allow today's completion
		lastCompletedYesterday := yesterday
		assert.False(t, lastCompletedYesterday.Equal(today), "Yesterday completion should allow today's completion")
		assert.True(t, lastCompletedYesterday.Equal(yesterday), "Yesterday completion should be detected correctly")

		// Test case 3: Gap in completion should reset streak
		twoDaysAgo := today.Add(-48 * time.Hour)
		assert.False(t, twoDaysAgo.Equal(today), "Two days ago should not equal today")
		assert.False(t, twoDaysAgo.Equal(yesterday), "Two days ago should not equal yesterday")
	})

	t.Run("Test_Milestone_Progression_Logic", func(t *testing.T) {
		// Test the milestone progression logic for various scenarios

		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 2, Points: 100},
			{Days: 3, Points: 200},
			{Days: 7, Points: 500},
		}

		service := &TaskProgressService{}

		testCases := []struct {
			name           string
			currentStreak  int
			expectedNext   *int // nil if no next milestone
			expectedPoints *int // nil if no next milestone
		}{
			{
				name:           "No streak - should target 1-day milestone",
				currentStreak:  0,
				expectedNext:   &[]int{1}[0],
				expectedPoints: &[]int{50}[0],
			},
			{
				name:           "1-day streak - should target 2-day milestone",
				currentStreak:  1,
				expectedNext:   &[]int{2}[0],
				expectedPoints: &[]int{100}[0],
			},
			{
				name:           "2-day streak - should target 3-day milestone",
				currentStreak:  2,
				expectedNext:   &[]int{3}[0],
				expectedPoints: &[]int{200}[0],
			},
			{
				name:           "3-day streak - should target 7-day milestone",
				currentStreak:  3,
				expectedNext:   &[]int{7}[0],
				expectedPoints: &[]int{500}[0],
			},
			{
				name:          "7+ day streak - all milestones completed",
				currentStreak: 7,
				expectedNext:  nil,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				nextMilestone := service.getNextMilestone(milestones, tc.currentStreak)

				if tc.expectedNext == nil {
					assert.Nil(t, nextMilestone, "Should have no next milestone")
				} else {
					require.NotNil(t, nextMilestone, "Should have next milestone")
					assert.Equal(t, *tc.expectedNext, nextMilestone.Days, "Next milestone days should match")
					assert.Equal(t, *tc.expectedPoints, nextMilestone.Points, "Next milestone points should match")
				}
			})
		}
	})

	t.Run("Test_Target_Value_Initialization", func(t *testing.T) {
		// Test that target values are correctly initialized for milestone-based tasks

		milestones := []model.ConsecutiveCheckinMilestone{
			{Days: 1, Points: 50},
			{Days: 3, Points: 200},
			{Days: 7, Points: 500},
		}

		service := &TaskProgressService{}

		// Test getFirstMilestone
		firstMilestone := service.getFirstMilestone(milestones)
		require.NotNil(t, firstMilestone, "Should have first milestone")
		assert.Equal(t, 1, firstMilestone.Days, "First milestone should be 1 day")
		assert.Equal(t, 50, firstMilestone.Points, "First milestone should be 50 points")

		// Test getHighestMilestone
		highestMilestone := service.getHighestMilestone(milestones)
		require.NotNil(t, highestMilestone, "Should have highest milestone")
		assert.Equal(t, 7, highestMilestone.Days, "Highest milestone should be 7 days")
		assert.Equal(t, 500, highestMilestone.Points, "Highest milestone should be 500 points")
	})

	t.Run("Test_Streak_Reset_Logic", func(t *testing.T) {
		// Test that streak reset logic works correctly

		today := time.Now().Truncate(24 * time.Hour)
		yesterday := today.Add(-24 * time.Hour)
		twoDaysAgo := today.Add(-48 * time.Hour)

		// Test consecutive day logic
		assert.True(t, yesterday.Equal(today.Add(-24*time.Hour)), "Yesterday calculation should be correct")

		// Test gap detection
		assert.False(t, twoDaysAgo.Equal(yesterday), "Two days ago should not equal yesterday")
		assert.True(t, twoDaysAgo.Before(yesterday), "Two days ago should be before yesterday")

		// Test that gaps > 1 day should reset streak
		gapDays := int(today.Sub(twoDaysAgo).Hours() / 24)
		assert.Greater(t, gapDays, 1, "Gap should be greater than 1 day")
	})

	t.Run("Test_Progress_Value_Sync", func(t *testing.T) {
		// Test that progressValue syncs correctly with streakCount

		testCases := []struct {
			name             string
			streakCount      int
			expectedProgress int
		}{
			{"No streak", 0, 0},
			{"1 day streak", 1, 1},
			{"3 day streak", 3, 3},
			{"7 day streak", 7, 7},
			{"30 day streak", 30, 30},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				// For consecutive check-in tasks, progressValue should always equal streakCount
				assert.Equal(t, tc.expectedProgress, tc.streakCount,
					"Progress value should equal streak count for consecutive check-in tasks")
			})
		}
	})
}

// TestConsecutiveCheckinTaskCompletionFlow tests the complete task completion flow
func TestConsecutiveCheckinTaskCompletionFlow(t *testing.T) {
	t.Run("Test_Task_Completion_Flow_Logic", func(t *testing.T) {
		// Test the logical flow of task completion without actual service calls

		// Simulate task with 1-day milestone
		task := &model.ActivityTask{
			ID:             uuid.New(),
			TaskIdentifier: &[]model.TaskIdentifier{model.TaskIDConsecutiveCheckinConfigurable}[0],
			Frequency:      model.FrequencyDaily,
			Conditions: &model.TaskConditions{
				ConsecutiveCheckinMilestones: []model.ConsecutiveCheckinMilestone{
					{Days: 1, Points: 50, Name: &model.MultilingualName{En: "Daily 1"}},
					{Days: 2, Points: 200, Name: &model.MultilingualName{En: "Daily 2"}},
				},
			},
		}

		// Test task configuration
		assert.Equal(t, model.TaskIDConsecutiveCheckinConfigurable, *task.TaskIdentifier)
		assert.Equal(t, model.FrequencyDaily, task.Frequency)
		assert.NotNil(t, task.Conditions)
		assert.Len(t, task.Conditions.ConsecutiveCheckinMilestones, 2)

		// Test first milestone
		firstMilestone := task.Conditions.ConsecutiveCheckinMilestones[0]
		assert.Equal(t, 1, firstMilestone.Days)
		assert.Equal(t, 50, firstMilestone.Points)
		assert.Equal(t, "Daily 1", firstMilestone.Name.En)

		// Test that the task is properly configured for the fix
		assert.True(t, len(task.Conditions.ConsecutiveCheckinMilestones) > 0,
			"Task should have milestones configured")
		assert.Equal(t, 1, task.Conditions.ConsecutiveCheckinMilestones[0].Days,
			"First milestone should be 1 day (the problematic case)")
	})
}
