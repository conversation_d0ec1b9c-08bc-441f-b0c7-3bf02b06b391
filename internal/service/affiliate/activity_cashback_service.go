package affiliate

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"go.uber.org/zap"

	"github.com/shopspring/decimal"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/global"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/model"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/agent_referral"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/repo/transaction"
	activityCashbackService "gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/service/activity_cashback"
	"gitlab.ggwp.life/xbit/xbit-dex/xbit-agent/internal/utils"
)

// ActivityCashbackService handles activity transaction cashback calculations and commission distribution
type ActivityCashbackService struct {
	affiliateRepo        repo.AffiliateRepositoryInterface
	userRepo             transaction.UserRepositoryInterface
	levelRepo            repo.LevelRepo
	activityCashbackRepo repo.ActivityCashbackRepositoryInterface
	memeCommissionRepo   transaction.MemeCommissionLedgerRepositoryInterface
	invitationRepo       *agent_referral.InvitationRepository
	taskRepo             activity_cashback.ActivityTaskRepositoryInterface
	unlimitedTaskRepo    activity_cashback.UnlimitedTaskCompletionRepositoryInterface
	tierService          activityCashbackService.TierManagementServiceInterface
}

// NewActivityCashbackService creates a new activity cashback service
func NewActivityCashbackService() *ActivityCashbackService {
	return &ActivityCashbackService{
		affiliateRepo:        repo.NewAffiliateRepository(),
		userRepo:             transaction.NewUserRepository(),
		levelRepo:            repo.NewLevelRepository(),
		activityCashbackRepo: repo.NewActivityCashbackRepository(),
		memeCommissionRepo:   transaction.NewMemeCommissionLedgerRepository(),
		invitationRepo:       &agent_referral.InvitationRepository{},
		taskRepo:             activity_cashback.NewActivityTaskRepository(),
		unlimitedTaskRepo:    activity_cashback.NewUnlimitedTaskCompletionRepository(),
		tierService: activityCashbackService.NewTierManagementService(
			activity_cashback.NewUserTierInfoRepository(),
			activity_cashback.NewTierBenefitRepository(),
		),
	}
}

// ProcessActivityTransactionCashback processes activity transaction cashback and commission distribution
// This method implements the cashback logic for user activities
func (s *ActivityCashbackService) ProcessActivityTransactionCashback(ctx context.Context, affiliateTx *model.AffiliateTransaction) error {
	if affiliateTx == nil {
		return nil
	}
	global.GVA_LOG.Info("Processing activity transaction cashback",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("platform_fee_mint", affiliateTx.PlatformFeeMint),
		zap.String("platform_fee_amount", affiliateTx.PlatformFeeAmount.String()),
		zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
		zap.String("fee_rate", affiliateTx.FeeRate.String()),
		zap.String("status", string(affiliateTx.Status)))

	// Update FirstTransactionAt if this is the user's first transaction
	err := s.updateFirstTransactionAt(ctx, affiliateTx)
	if err != nil {
		global.GVA_LOG.Error("Failed to update FirstTransactionAt",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.Error(err))
		// Continue processing even if FirstTransactionAt update fails
	}

	if affiliateTx.Status != "Completed" {
		global.GVA_LOG.Debug("Transaction not completed, skipping cashback",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("status", string(affiliateTx.Status)))
		return nil
	}

	// Check if cashback already exists for this transaction
	existingCashback, err := s.activityCashbackRepo.GetActivityCashbackByTransactionID(ctx, affiliateTx.ID)
	if err == nil && existingCashback != nil {
		global.GVA_LOG.Debug("Cashback already exists for transaction",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("cashback_id", existingCashback.ID.String()))
		return nil
	}

	// Query user's tier UserTierInfo.CurrentTier
	// Then query TierBenefit.CashbackPercentage based on the tier
	userTierInfo, err := s.getUserWithTierInfo(ctx, affiliateTx.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user tier info: %w", err)
	}

	solPrice, err := s.getSolPriceForTransaction(ctx, affiliateTx)
	if err != nil {
		return fmt.Errorf("failed to get SOL price: %w", err)
	}

	if affiliateTx.PlatformFeeMint != utils.WSOL_ADDRESS {
		global.GVA_LOG.Warn("Unsupported platform fee mint, skipping cashback")
		return nil
	}

	if affiliateTx.ChainID != (string)(utils.ChainIDSolana) {
		global.GVA_LOG.Warn("Unsupported chain id, skipping cashback")
		return nil
	}

	// fee amount in SOL
	platformFeeAmount := affiliateTx.PlatformFeeAmount

	if platformFeeAmount.LessThanOrEqual(decimal.Zero) {
		platformFeeAmount = affiliateTx.QuoteAmount.Mul(affiliateTx.FeeRate).Truncate(utils.SOL_DECIMALS)

		if platformFeeAmount.LessThanOrEqual(decimal.Zero) {
			global.GVA_LOG.Warn("Calculated platform fee amount is zero or negative, skipping cashback",
				zap.String("order_id", affiliateTx.OrderID.String()),
				zap.String("user_id", affiliateTx.UserID.String()),
				zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
				zap.String("platform_fee", affiliateTx.PlatformFeeAmount.String()),
				zap.String("fee_rate", affiliateTx.FeeRate.String()),
			)
			return nil
		}
	}

	// calculate cashback amount in SOL using tier benefit cashback percentage
	cashbackAmountSOL := platformFeeAmount.Mul(userTierInfo.TierBenefit.CashbackPercentage).Truncate(utils.SOL_DECIMALS)
	if cashbackAmountSOL.LessThanOrEqual(decimal.Zero) {
		global.GVA_LOG.Warn("Calculated cashback amount is zero or negative, skipping cashback",
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
			zap.String("platform_fee", platformFeeAmount.String()),
			zap.String("fee_rate", affiliateTx.FeeRate.String()),
			zap.Int("user_tier", userTierInfo.CurrentTier),
			zap.String("tier_name", userTierInfo.TierBenefit.TierName),
			zap.String("cashback_percentage", userTierInfo.TierBenefit.CashbackPercentage.String()),
		)
		return nil
	}

	cashbackAmountUSD := cashbackAmountSOL.Mul(solPrice.Price).Truncate(9)

	global.GVA_LOG.Info("Calculated activity cashback",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.Int("user_tier", userTierInfo.CurrentTier),
		zap.String("tier_name", userTierInfo.TierBenefit.TierName),
		zap.String("quote_amount", affiliateTx.QuoteAmount.String()),
		zap.String("sol_price", solPrice.Price.String()),
		zap.String("cashback_percentage", userTierInfo.TierBenefit.CashbackPercentage.String()),
		zap.String("cashback_amount_usd", cashbackAmountUSD.String()),
		// zap.String("cashback_amount_sol", cashbackAmountSOL.String()),
	)

	cashback := &model.ActivityCashback{
		UserID:                 affiliateTx.UserID,
		UserAddress:            affiliateTx.UserAddress,
		Status:                 "PENDING_CLAIM",
		AffiliateTransactionID: affiliateTx.ID,
		SolPriceUSD:            solPrice.Price,
		CashbackAmountUSD:      cashbackAmountUSD,
		CashbackAmountSOL:      cashbackAmountSOL,
		CreatedAt:              &time.Time{},
	}

	now := time.Now()
	cashback.CreatedAt = &now

	if err := s.activityCashbackRepo.CreateActivityCashback(ctx, cashback); err != nil {
		return fmt.Errorf("failed to create activity cashback record: %w", err)
	}

	global.GVA_LOG.Info("Activity transaction cashback processed successfully",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("cashback_id", cashback.ID.String()),
		zap.String("total_cashback_usd", cashbackAmountUSD.String()),
		// zap.String("total_cashback_sol", cashbackAmountSOL.String()),
	)

	return nil
}

func (s *ActivityCashbackService) getUserWithLevel(ctx context.Context, userID uuid.UUID) (*model.User, error) {
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Preload("AgentLevel").
		Where("id = ?", userID).
		First(&user).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user: %w", err)
	}
	return &user, nil
}

// getUserWithTierInfo gets user with tier information for activity cashback calculation
func (s *ActivityCashbackService) getUserWithTierInfo(ctx context.Context, userID uuid.UUID) (*model.UserTierInfo, error) {
	var userTierInfo model.UserTierInfo
	err := global.GVA_DB.WithContext(ctx).
		Preload("TierBenefit").
		Where("user_id = ?", userID).
		First(&userTierInfo).Error
	if err != nil {
		return nil, fmt.Errorf("failed to get user tier info: %w", err)
	}
	return &userTierInfo, nil
}

// getSolPriceForTransaction gets the SOL price for the transaction
// We try to get the price at the transaction time, or the latest available price
func (s *ActivityCashbackService) getSolPriceForTransaction(ctx context.Context, affiliateTx *model.AffiliateTransaction) (*model.SolPriceSnapshot, error) {
	// If no price found at transaction time, get the latest available price
	latestPrice, err := s.affiliateRepo.GetLatestSolPrice(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get latest SOL price: %w", err)
	}

	global.GVA_LOG.Warn("Using latest SOL price instead of transaction time price",
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.String("latest_price", latestPrice.Price.String()),
		zap.Time("latest_price_timestamp", latestPrice.Timestamp),
		zap.Time("tx_timestamp", affiliateTx.CreatedAt))

	return latestPrice, nil
}

// updateFirstTransactionAt updates the FirstTransactionAt field for a user if it's their first transaction
func (s *ActivityCashbackService) updateFirstTransactionAt(ctx context.Context, affiliateTx *model.AffiliateTransaction) error {
	// Check if user already has FirstTransactionAt set
	var user model.User
	err := global.GVA_DB.WithContext(ctx).
		Select("id, first_transaction_at").
		Where("id = ?", affiliateTx.UserID).
		First(&user).Error
	if err != nil {
		// If user not found, log it but don't treat as error
		// This might happen if user hasn't been created in the system yet
		global.GVA_LOG.Debug("User not found when updating FirstTransactionAt",
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.String("order_id", affiliateTx.OrderID.String()),
			zap.Error(err))
		return nil
	}

	// If FirstTransactionAt is already set, no need to update
	// if user.FirstTransactionAt != nil {
	// 	global.GVA_LOG.Debug("FirstTransactionAt already set for user",
	// 		zap.String("user_id", affiliateTx.UserID.String()),
	// 		zap.String("order_id", affiliateTx.OrderID.String()),
	// 		zap.Time("first_transaction_at", *user.FirstTransactionAt))
	// 	return nil
	// }

	// Update FirstTransactionAt with current transaction time
	now := time.Now()
	err = global.GVA_DB.WithContext(ctx).
		Model(&model.User{}).
		Where("id = ?", affiliateTx.UserID).
		Update("first_transaction_at", &now).Error
	if err != nil {
		return fmt.Errorf("failed to update FirstTransactionAt: %w", err)
	}

	global.GVA_LOG.Info("Updated FirstTransactionAt for user",
		zap.String("user_id", affiliateTx.UserID.String()),
		zap.String("order_id", affiliateTx.OrderID.String()),
		zap.Time("first_transaction_at", now))

	// Trigger invite friends task completion for all referrers
	if err := s.processInviteFriendsTaskCompletion(ctx, affiliateTx.UserID); err != nil {
		global.GVA_LOG.Error("Failed to process invite friends task completion",
			zap.String("user_id", affiliateTx.UserID.String()),
			zap.Error(err))
		// Don't return error here as FirstTransactionAt update was successful
	}

	return nil
}

// processInviteFriendsTaskCompletion processes invite friends task completion for all referrers of a user
func (s *ActivityCashbackService) processInviteFriendsTaskCompletion(ctx context.Context, userID uuid.UUID) error {
	// Get all direct referrers of this user
	referrerIDs, err := s.invitationRepo.GetDirectReferrers(ctx, userID)
	if err != nil {
		return fmt.Errorf("failed to get direct referrers: %w", err)
	}

	if len(referrerIDs) == 0 {
		global.GVA_LOG.Debug("No direct referrers found for user", zap.String("user_id", userID.String()))
		return nil
	}

	global.GVA_LOG.Info("Processing invite friends task completion",
		zap.String("invited_user_id", userID.String()),
		zap.Int("referrer_count", len(referrerIDs)))

	// For each referrer, trigger invite friends task completion
	for _, referrerID := range referrerIDs {
		if err := s.triggerInviteFriendsTaskForReferrer(ctx, referrerID, userID); err != nil {
			global.GVA_LOG.Error("Failed to trigger invite friends task for referrer",
				zap.String("referrer_id", referrerID.String()),
				zap.String("invited_user_id", userID.String()),
				zap.Error(err))
			// Continue processing other referrers
			continue
		}

		global.GVA_LOG.Info("Successfully triggered invite friends task completion",
			zap.String("referrer_id", referrerID.String()),
			zap.String("invited_user_id", userID.String()))
	}

	return nil
}

// triggerInviteFriendsTaskForReferrer triggers invite friends task completion for a specific referrer
func (s *ActivityCashbackService) triggerInviteFriendsTaskForReferrer(ctx context.Context, referrerID, invitedUserID uuid.UUID) error {
	// Get the invite friends task
	task, err := s.taskRepo.GetByTaskIdentifier(ctx, model.TaskIDInviteFriends)
	if err != nil {
		return fmt.Errorf("failed to get invite friends task: %w", err)
	}

	// Check if task is active and available
	if !task.IsActive {
		global.GVA_LOG.Warn("Invite friends task is not active",
			zap.String("task_id", task.ID.String()))
		return nil
	}

	if !task.IsAvailable() {
		global.GVA_LOG.Warn("Invite friends task is not available",
			zap.String("task_id", task.ID.String()))
		return nil
	}

	// Create verification data
	verificationData := map[string]interface{}{
		"invited_user_id":   invitedUserID.String(),
		"completion_method": "first_transaction_trigger",
		"source":            "affiliate_service",
		"trigger_time":      time.Now(),
	}

	// For unlimited tasks, we need to create a completion record directly
	// Since this is triggered by first transaction, we bypass the normal task completion flow
	if err := s.createInviteFriendsTaskCompletion(ctx, referrerID, task, verificationData); err != nil {
		return fmt.Errorf("failed to create invite friends task completion: %w", err)
	}

	global.GVA_LOG.Info("Successfully triggered invite friends task completion",
		zap.String("referrer_id", referrerID.String()),
		zap.String("invited_user_id", invitedUserID.String()),
		zap.String("task_id", task.ID.String()),
		zap.Int("points_awarded", task.Points))

	return nil
}

// createInviteFriendsTaskCompletion creates a task completion record for invite friends task
func (s *ActivityCashbackService) createInviteFriendsTaskCompletion(ctx context.Context, userID uuid.UUID, task *model.ActivityTask, verificationData map[string]interface{}) error {
	// Create unlimited task completion record
	completion := &model.UnlimitedTaskCompletion{
		UserID:         userID,
		TaskID:         task.ID,
		PointsAwarded:  task.Points,
		CompletionDate: time.Now(),
		SequenceNumber: 1, // This will be auto-incremented by the database
	}

	// Set verification data using the proper method
	completion.SetVerificationData("first_transaction_trigger", "affiliate_service", verificationData)

	// Create the completion record
	if err := s.unlimitedTaskRepo.Create(ctx, completion); err != nil {
		return fmt.Errorf("failed to create unlimited task completion: %w", err)
	}

	// Award points to user using tier management service
	source := fmt.Sprintf("invite_friends_task:%s", completion.ID.String())
	if err := s.tierService.AddPoints(ctx, userID, task.Points, source); err != nil {
		global.GVA_LOG.Error("Failed to add points for invite friends task completion",
			zap.Error(err),
			zap.String("user_id", userID.String()),
			zap.String("task_id", task.ID.String()),
			zap.Int("points", task.Points))
		// Don't return error here as the task completion record is already created
		// The points can be awarded later through manual intervention if needed
	} else {
		global.GVA_LOG.Info("Successfully awarded points for invite friends task completion",
			zap.String("user_id", userID.String()),
			zap.String("task_id", task.ID.String()),
			zap.String("completion_id", completion.ID.String()),
			zap.Int("points", task.Points),
			zap.String("source", source))
	}

	// Check for tier upgrade after adding points
	if _, err := s.tierService.CheckTierUpgrade(ctx, userID); err != nil {
		global.GVA_LOG.Error("Failed to check tier upgrade after invite friends completion",
			zap.Error(err),
			zap.String("user_id", userID.String()))
		// Don't return error here as points were already added successfully
	}

	return nil
}
